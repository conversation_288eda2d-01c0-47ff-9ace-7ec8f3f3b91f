# -*- coding: utf-8 -*-
{
    'name': 'Student Management',
    'version': '1.0.0',
    'category': 'Education',
    'summary': 'Student Management System for Learning Odoo Backend',
    'description': """
        A comprehensive student management system to learn Odoo backend development.
        This module includes:
        - Student registration and management
        - Course management
        - Grade tracking
        - Basic reporting
        
        This is a learning module to understand Odoo backend concepts including:
        - Models and fields
        - Views (form, tree, kanban)
        - Security and access rights
        - Workflows and business logic
        - Reports
    """,
    'author': 'Learning Developer',
    'website': 'https://www.odoo.com',
    'depends': ['base', 'mail'],
    'data': [
        'security/ir.model.access.csv',
        'views/student_views.xml',
        'views/course_views.xml',
        'views/menu_views.xml',
        'data/student_data.xml',
    ],
    'demo': [
        'demo/student_demo.xml',
    ],
    'installable': True,
    'application': True,
    'auto_install': False,
    'license': 'LGPL-3',
}
